{% extends 'base_admin_dashboard.html' %}
{% load static %}

{% block page_title %}Spare Shop Management{% endblock %}
{% block page_description %}Comprehensive spare parts inventory management with advanced features and M-Pesa integration{% endblock %}

{% block dashboard_content %}
<div class="space-y-8">
    <!-- Enhanced Header with Modern Actions -->
    <div class="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4 animate-fade-in-up" style="animation-delay: 0.1s;">
        <div>
            <h2 class="text-3xl font-bold text-harrier-dark font-montserrat">Spare Shop Management</h2>
            <p class="text-gray-600 mt-1 font-raleway">Comprehensive spare parts inventory management with advanced features and M-Pesa integration</p>
        </div>

        <div class="flex flex-wrap gap-3">
            <button onclick="openFilterModal()" class="enhanced-btn enhanced-btn-secondary text-sm">
                <i class="fas fa-filter mr-2"></i>Advanced Filters
            </button>
            <button onclick="refreshInventory()" class="enhanced-btn enhanced-btn-secondary text-sm">
                <i class="fas fa-sync-alt mr-2"></i>Refresh
            </button>
            <button onclick="exportInventoryData()" class="enhanced-btn enhanced-btn-secondary text-sm">
                <i class="fas fa-download mr-2"></i>Export Data
            </button>
            <button onclick="openAddPartModal()" class="enhanced-btn enhanced-btn-primary text-sm">
                <i class="fas fa-plus mr-2"></i>Add Spare Part
            </button>
        </div>
    </div>

    <!-- Enhanced Inventory Stats with Real-time Updates -->
    <div id="inventory-stats-container" class="grid grid-cols-1 md:grid-cols-4 gap-6 animate-fade-in-up" style="animation-delay: 0.2s;">
        <!-- Total Parts Card -->
        <div class="glassmorphism-card stat-card-enhanced group hover:scale-105 transition-all duration-300"
             hx-get="/dashboard/admin/spare-shop/stats/total/"
             hx-trigger="every 30s"
             hx-target="#total-parts-stat"
             hx-swap="innerHTML">
            <div class="flex items-center justify-between">
                <div>
                    <div id="total-parts-stat" class="stat-value text-amber-600 font-montserrat">{{ total_parts|default:0 }}</div>
                    <div class="stat-label font-raleway">Total Parts</div>
                    <div class="stat-trend text-xs text-green-600 mt-1">
                        <i class="fas fa-arrow-up mr-1"></i>
                        <span id="total-parts-trend">+12% this month</span>
                    </div>
                </div>
                <div class="stat-icon bg-gradient-to-br from-amber-500 to-amber-600 group-hover:rotate-12 transition-transform duration-300">
                    <i class="fas fa-cogs text-white"></i>
                </div>
            </div>
            <div class="stat-progress-bar">
                <div class="stat-progress bg-amber-500" style="width: 85%"></div>
            </div>
        </div>

        <!-- In Stock Card -->
        <div class="glassmorphism-card stat-card-enhanced group hover:scale-105 transition-all duration-300"
             hx-get="/dashboard/admin/spare-shop/stats/in-stock/"
             hx-trigger="every 30s"
             hx-target="#in-stock-stat"
             hx-swap="innerHTML">
            <div class="flex items-center justify-between">
                <div>
                    <div id="in-stock-stat" class="stat-value text-green-600 font-montserrat">{{ in_stock_parts|default:0 }}</div>
                    <div class="stat-label font-raleway">In Stock</div>
                    <div class="stat-trend text-xs text-green-600 mt-1">
                        <i class="fas fa-check-circle mr-1"></i>
                        <span id="in-stock-percentage">{{ in_stock_parts|default:0 }}% available</span>
                    </div>
                </div>
                <div class="stat-icon bg-gradient-to-br from-green-500 to-green-600 group-hover:scale-110 transition-transform duration-300">
                    <i class="fas fa-check text-white"></i>
                </div>
            </div>
            <div class="stat-progress-bar">
                <div class="stat-progress bg-green-500" style="width: 92%"></div>
            </div>
        </div>

        <!-- Low Stock Card -->
        <div class="glassmorphism-card stat-card-enhanced group hover:scale-105 transition-all duration-300"
             hx-get="/dashboard/admin/spare-shop/stats/low-stock/"
             hx-trigger="every 30s"
             hx-target="#low-stock-stat"
             hx-swap="innerHTML">
            <div class="flex items-center justify-between">
                <div>
                    <div id="low-stock-stat" class="stat-value text-orange-600 font-montserrat">{{ low_stock_parts|default:0 }}</div>
                    <div class="stat-label font-raleway">Low Stock</div>
                    <div class="stat-trend text-xs text-orange-600 mt-1">
                        <i class="fas fa-exclamation-triangle mr-1 animate-pulse"></i>
                        <span>Needs attention</span>
                    </div>
                </div>
                <div class="stat-icon bg-gradient-to-br from-orange-500 to-orange-600 group-hover:animate-bounce">
                    <i class="fas fa-exclamation-triangle text-white"></i>
                </div>
            </div>
            <div class="stat-progress-bar">
                <div class="stat-progress bg-orange-500" style="width: 15%"></div>
            </div>
        </div>

        <!-- Out of Stock Card -->
        <div class="glassmorphism-card stat-card-enhanced group hover:scale-105 transition-all duration-300"
             hx-get="/dashboard/admin/spare-shop/stats/out-of-stock/"
             hx-trigger="every 30s"
             hx-target="#out-of-stock-stat"
             hx-swap="innerHTML">
            <div class="flex items-center justify-between">
                <div>
                    <div id="out-of-stock-stat" class="stat-value text-red-600 font-montserrat">{{ out_of_stock_parts|default:0 }}</div>
                    <div class="stat-label font-raleway">Out of Stock</div>
                    <div class="stat-trend text-xs text-red-600 mt-1">
                        <i class="fas fa-times-circle mr-1 animate-pulse"></i>
                        <span>Reorder needed</span>
                    </div>
                </div>
                <div class="stat-icon bg-gradient-to-br from-red-500 to-red-600 group-hover:animate-pulse">
                    <i class="fas fa-times text-white"></i>
                </div>
            </div>
            <div class="stat-progress-bar">
                <div class="stat-progress bg-red-500" style="width: 8%"></div>
            </div>
        </div>
    </div>

    <!-- Additional Analytics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 animate-fade-in-up" style="animation-delay: 0.25s;">
        <!-- Stock Value Card -->
        <div class="glassmorphism-card stat-card-enhanced group hover:scale-105 transition-all duration-300">
            <div class="flex items-center justify-between">
                <div>
                    <div class="stat-value text-purple-600 font-montserrat">KSh {{ total_stock_value|floatformat:0|default:0 }}</div>
                    <div class="stat-label font-raleway">Total Stock Value</div>
                    <div class="stat-trend text-xs text-purple-600 mt-1">
                        <i class="fas fa-chart-line mr-1"></i>
                        <span>Inventory worth</span>
                    </div>
                </div>
                <div class="stat-icon bg-gradient-to-br from-purple-500 to-purple-600">
                    <i class="fas fa-money-bill-wave text-white"></i>
                </div>
            </div>
            <div class="stat-progress-bar">
                <div class="stat-progress bg-purple-500" style="width: 78%"></div>
            </div>
        </div>

        <!-- Average Price Card -->
        <div class="glassmorphism-card stat-card-enhanced group hover:scale-105 transition-all duration-300">
            <div class="flex items-center justify-between">
                <div>
                    <div class="stat-value text-indigo-600 font-montserrat">KSh {{ avg_price|floatformat:0|default:0 }}</div>
                    <div class="stat-label font-raleway">Average Price</div>
                    <div class="stat-trend text-xs text-indigo-600 mt-1">
                        <i class="fas fa-calculator mr-1"></i>
                        <span>Per part</span>
                    </div>
                </div>
                <div class="stat-icon bg-gradient-to-br from-indigo-500 to-indigo-600">
                    <i class="fas fa-calculator text-white"></i>
                </div>
            </div>
            <div class="stat-progress-bar">
                <div class="stat-progress bg-indigo-500" style="width: 65%"></div>
            </div>
        </div>

        <!-- Recent Activity Card -->
        <div class="glassmorphism-card stat-card-enhanced group hover:scale-105 transition-all duration-300">
            <div class="flex items-center justify-between">
                <div>
                    <div class="stat-value text-teal-600 font-montserrat">{{ recent_orders.count|default:0 }}</div>
                    <div class="stat-label font-raleway">Recent Orders</div>
                    <div class="stat-trend text-xs text-teal-600 mt-1">
                        <i class="fas fa-clock mr-1"></i>
                        <span>Last 24 hours</span>
                    </div>
                </div>
                <div class="stat-icon bg-gradient-to-br from-teal-500 to-teal-600">
                    <i class="fas fa-shopping-cart text-white"></i>
                </div>
            </div>
            <div class="stat-progress-bar">
                <div class="stat-progress bg-teal-500" style="width: 45%"></div>
            </div>
        </div>
    </div>

    <!-- Enhanced Quick Actions with Modern Design -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 animate-fade-in-up" style="animation-delay: 0.3s;">
        <div class="glassmorphism-card quick-action-card-enhanced group cursor-pointer" onclick="openMpesaConfigModal()">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <div class="action-icon bg-gradient-to-br from-green-500 to-green-600 group-hover:scale-110 transition-transform duration-300">
                        <i class="fas fa-mobile-alt text-white text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <h4 class="font-semibold text-harrier-dark font-montserrat">M-Pesa Integration</h4>
                        <p class="text-sm text-gray-600 font-raleway">Configure payment settings</p>
                        <div class="text-xs text-green-600 mt-1">
                            <i class="fas fa-check-circle mr-1"></i>Active
                        </div>
                    </div>
                </div>
                <div class="action-arrow opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>
            </div>
        </div>

        <div class="glassmorphism-card quick-action-card-enhanced group cursor-pointer" onclick="openSalesManagementPage()">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <div class="action-icon bg-gradient-to-br from-harrier-blue to-blue-600 group-hover:scale-110 transition-transform duration-300">
                        <i class="fas fa-chart-line text-white text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <h4 class="font-semibold text-harrier-dark font-montserrat">Sales Management</h4>
                        <p class="text-sm text-gray-600 font-raleway">Track sales and revenue</p>
                        <div class="text-xs text-blue-600 mt-1">
                            <i class="fas fa-trending-up mr-1"></i>+15% this week
                        </div>
                    </div>
                </div>
                <div class="action-arrow opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>
            </div>
        </div>

        <div class="glassmorphism-card quick-action-card-enhanced group cursor-pointer" onclick="openOrderManagementPage()">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <div class="action-icon bg-gradient-to-br from-harrier-red to-red-600 group-hover:scale-110 transition-transform duration-300">
                        <i class="fas fa-shopping-cart text-white text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <h4 class="font-semibold text-harrier-dark font-montserrat">Order Management</h4>
                        <p class="text-sm text-gray-600 font-raleway">Manage customer orders</p>
                        <div class="text-xs text-orange-600 mt-1">
                            <i class="fas fa-clock mr-1"></i>{{ recent_orders.count }} pending
                        </div>
                    </div>
                </div>
                <div class="action-arrow opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Spare Parts Inventory with Real-time Search -->
    <div class="glassmorphism-card animate-fade-in-up" style="animation-delay: 0.4s;">
        <div class="p-6 border-b border-gray-200">
            <div class="flex flex-col lg:flex-row items-start lg:items-center justify-between gap-4">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-gradient-to-br from-amber-500 to-amber-600 rounded-xl flex items-center justify-center mr-4 shadow-lg">
                        <i class="fas fa-cogs text-white text-xl"></i>
                    </div>
                    <div>
                        <h3 class="text-xl font-bold text-harrier-dark font-montserrat">Spare Parts Inventory</h3>
                        <p class="text-sm text-gray-600 font-raleway">Manage your complete spare parts inventory</p>
                    </div>
                </div>

                <!-- Advanced Search and Filter System -->
                <div class="flex flex-col gap-4 w-full lg:w-auto">
                    <!-- Primary Search Bar -->
                    <div class="flex flex-col sm:flex-row gap-3">
                        <!-- Real-time Search with Enhanced UI -->
                        <div class="relative flex-1 min-w-0">
                            <input type="text"
                                   id="inventory-search"
                                   name="search"
                                   value="{{ search_query }}"
                                   placeholder="Search parts, SKU, barcode, or vendor..."
                                   class="w-full pl-12 pr-10 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-harrier-red focus:border-transparent font-raleway text-sm bg-white shadow-sm"
                                   hx-get="{% url 'core:admin_spare_shop' %}"
                                   hx-trigger="keyup changed delay:300ms"
                                   hx-target="#inventory-table-container"
                                   hx-include="[name='category'], [name='availability'], [name='brand'], [name='price_range'], [name='condition']"
                                   hx-indicator="#search-loading">
                            <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                <i class="fas fa-search text-gray-400 text-lg"></i>
                            </div>
                            <div id="search-loading" class="absolute inset-y-0 right-0 pr-4 flex items-center htmx-indicator">
                                <i class="fas fa-spinner fa-spin text-harrier-red"></i>
                            </div>
                            {% if search_query %}
                            <button onclick="clearSearch()" class="absolute inset-y-0 right-0 pr-4 flex items-center text-gray-400 hover:text-gray-600">
                                <i class="fas fa-times"></i>
                            </button>
                            {% endif %}
                        </div>

                        <!-- Advanced Filter Toggle -->
                        <button onclick="toggleAdvancedFilters()"
                                id="advanced-filter-toggle"
                                class="enhanced-btn enhanced-btn-secondary px-4 py-3 whitespace-nowrap">
                            <i class="fas fa-sliders-h mr-2"></i>Advanced Filters
                            <i class="fas fa-chevron-down ml-2 transition-transform duration-200" id="filter-chevron"></i>
                        </button>
                    </div>

                    <!-- Advanced Filters Panel (Collapsible) -->
                    <div id="advanced-filters-panel" class="hidden">
                        <div class="glassmorphism-card p-4">
                            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4">
                                <!-- Category Filter -->
                                <div class="filter-group">
                                    <label class="filter-label">
                                        <i class="fas fa-tags mr-2 text-blue-600"></i>Category
                                    </label>
                                    <select name="category"
                                            class="enhanced-select w-full"
                                            hx-get="{% url 'core:admin_spare_shop' %}"
                                            hx-trigger="change"
                                            hx-target="#inventory-table-container"
                                            hx-include="[name='availability'], [name='brand'], [name='price_range'], [name='condition'], #inventory-search">
                                        <option value="">All Categories</option>
                                        {% for category in categories %}
                                            <option value="{{ category.id }}" {% if category.id|stringformat:"s" == current_category %}selected{% endif %}>
                                                {{ category.name }}
                                            </option>
                                        {% endfor %}
                                    </select>
                                </div>

                                <!-- Availability Filter -->
                                <div class="filter-group">
                                    <label class="filter-label">
                                        <i class="fas fa-boxes mr-2 text-green-600"></i>Stock Status
                                    </label>
                                    <select name="availability"
                                            class="enhanced-select w-full"
                                            hx-get="{% url 'core:admin_spare_shop' %}"
                                            hx-trigger="change"
                                            hx-target="#inventory-table-container"
                                            hx-include="[name='category'], [name='brand'], [name='price_range'], [name='condition'], #inventory-search">
                                        <option value="">All Status</option>
                                        <option value="available" {% if current_availability == 'available' %}selected{% endif %}>Available</option>
                                        <option value="low_stock" {% if current_availability == 'low_stock' %}selected{% endif %}>Low Stock</option>
                                        <option value="out_of_stock" {% if current_availability == 'out_of_stock' %}selected{% endif %}>Out of Stock</option>
                                    </select>
                                </div>

                                <!-- Brand Filter -->
                                <div class="filter-group">
                                    <label class="filter-label">
                                        <i class="fas fa-car mr-2 text-purple-600"></i>Brand
                                    </label>
                                    <select name="brand"
                                            class="enhanced-select w-full"
                                            hx-get="{% url 'core:admin_spare_shop' %}"
                                            hx-trigger="change"
                                            hx-target="#inventory-table-container"
                                            hx-include="[name='category'], [name='availability'], [name='price_range'], [name='condition'], #inventory-search">
                                        <option value="">All Brands</option>
                                        <option value="toyota">Toyota</option>
                                        <option value="nissan">Nissan</option>
                                        <option value="honda">Honda</option>
                                        <option value="mazda">Mazda</option>
                                        <option value="subaru">Subaru</option>
                                        <option value="mitsubishi">Mitsubishi</option>
                                    </select>
                                </div>

                                <!-- Price Range Filter -->
                                <div class="filter-group">
                                    <label class="filter-label">
                                        <i class="fas fa-dollar-sign mr-2 text-emerald-600"></i>Price Range
                                    </label>
                                    <select name="price_range"
                                            class="enhanced-select w-full"
                                            hx-get="{% url 'core:admin_spare_shop' %}"
                                            hx-trigger="change"
                                            hx-target="#inventory-table-container"
                                            hx-include="[name='category'], [name='availability'], [name='brand'], [name='condition'], #inventory-search">
                                        <option value="">All Prices</option>
                                        <option value="0-1000">Under KSh 1,000</option>
                                        <option value="1000-5000">KSh 1,000 - 5,000</option>
                                        <option value="5000-10000">KSh 5,000 - 10,000</option>
                                        <option value="10000-50000">KSh 10,000 - 50,000</option>
                                        <option value="50000+">Over KSh 50,000</option>
                                    </select>
                                </div>

                                <!-- Condition Filter -->
                                <div class="filter-group">
                                    <label class="filter-label">
                                        <i class="fas fa-star mr-2 text-orange-600"></i>Condition
                                    </label>
                                    <select name="condition"
                                            class="enhanced-select w-full"
                                            hx-get="{% url 'core:admin_spare_shop' %}"
                                            hx-trigger="change"
                                            hx-target="#inventory-table-container"
                                            hx-include="[name='category'], [name='availability'], [name='brand'], [name='price_range'], #inventory-search">
                                        <option value="">All Conditions</option>
                                        <option value="new">New</option>
                                        <option value="used">Used</option>
                                        <option value="refurbished">Refurbished</option>
                                    </select>
                                </div>
                            </div>

                            <!-- Filter Actions -->
                            <div class="flex justify-between items-center mt-4 pt-4 border-t border-gray-200">
                                <div class="text-sm text-gray-600">
                                    <span id="filter-count">{{ spare_parts.paginator.count|default:0 }}</span> parts found
                                </div>
                                <div class="flex gap-2">
                                    <button onclick="clearAllFilters()"
                                            class="enhanced-btn enhanced-btn-secondary text-sm px-3 py-2">
                                        <i class="fas fa-times mr-1"></i>Clear All
                                    </button>
                                    <button onclick="saveFilterPreset()"
                                            class="enhanced-btn enhanced-btn-secondary text-sm px-3 py-2">
                                        <i class="fas fa-save mr-1"></i>Save Preset
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Action Buttons -->
                    <div class="flex flex-wrap gap-2">
                        <button onclick="refreshInventory()"
                                class="enhanced-btn enhanced-btn-secondary text-sm px-3 py-2">
                            <i class="fas fa-sync-alt mr-1"></i>Refresh
                        </button>
                        <button onclick="exportInventoryData()"
                                class="enhanced-btn enhanced-btn-secondary text-sm px-3 py-2">
                            <i class="fas fa-download mr-1"></i>Export
                        </button>
                        <button onclick="bulkActions()"
                                class="enhanced-btn enhanced-btn-secondary text-sm px-3 py-2">
                            <i class="fas fa-tasks mr-1"></i>Bulk Actions
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Enhanced Table Container with Loading States -->
        <div id="inventory-table-container" class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gradient-to-r from-gray-50 to-gray-100">
                    <tr>
                        <th class="px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider font-montserrat">
                            <div class="flex items-center">
                                <i class="fas fa-cog mr-2 text-amber-600"></i>
                                Part Details
                            </div>
                        </th>
                        <th class="px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider font-montserrat">
                            <div class="flex items-center">
                                <i class="fas fa-tags mr-2 text-blue-600"></i>
                                Category
                            </div>
                        </th>
                        <th class="px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider font-montserrat">
                            <div class="flex items-center">
                                <i class="fas fa-boxes mr-2 text-green-600"></i>
                                Stock Status
                            </div>
                        </th>
                        <th class="px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider font-montserrat">
                            <div class="flex items-center">
                                <i class="fas fa-dollar-sign mr-2 text-emerald-600"></i>
                                Price
                            </div>
                        </th>
                        <th class="px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider font-montserrat">
                            <div class="flex items-center">
                                <i class="fas fa-truck mr-2 text-purple-600"></i>
                                Supplier
                            </div>
                        </th>
                        <th class="px-6 py-4 text-center text-xs font-bold text-gray-700 uppercase tracking-wider font-montserrat">
                            <div class="flex items-center justify-center">
                                <i class="fas fa-tools mr-2 text-harrier-red"></i>
                                Actions
                            </div>
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% if spare_parts %}
                        {% for part in spare_parts %}
                        <tr class="hover:bg-gray-50 transition-colors duration-200">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    {% if part.image %}
                                        <img src="{{ part.image.url }}" alt="{{ part.name }}" class="w-12 h-12 object-cover rounded-lg border-2 border-gray-200 mr-4">
                                    {% else %}
                                        <div class="w-12 h-12 bg-gradient-to-br from-amber-200 to-amber-300 rounded-lg flex items-center justify-center mr-4">
                                            <i class="fas fa-cogs text-amber-600 text-lg"></i>
                                        </div>
                                    {% endif %}
                                    <div>
                                        <div class="text-sm font-medium text-harrier-dark font-raleway">{{ part.name }}</div>
                                        <div class="text-sm text-gray-500">{{ part.part_number }}</div>
                                        {% if part.sku %}
                                        <div class="text-xs text-gray-600">
                                            <i class="fas fa-barcode mr-1"></i>{{ part.sku }}
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 font-montserrat">
                                    {{ part.category_new.name|default:part.category|default:"Uncategorized" }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    {% if part.stock_quantity > 10 %}
                                        <div class="w-3 h-3 bg-green-400 rounded-full mr-2"></div>
                                        <span class="text-sm text-green-800 font-medium">In Stock</span>
                                    {% elif part.stock_quantity > 0 %}
                                        <div class="w-3 h-3 bg-orange-400 rounded-full mr-2"></div>
                                        <span class="text-sm text-orange-800 font-medium">Low Stock</span>
                                    {% else %}
                                        <div class="w-3 h-3 bg-red-400 rounded-full mr-2"></div>
                                        <span class="text-sm text-red-800 font-medium">Out of Stock</span>
                                    {% endif %}
                                </div>
                                <div class="text-xs text-gray-500 mt-1">{{ part.stock_quantity }} units</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-semibold text-harrier-dark">KSh {{ part.price|floatformat:0 }}</div>
                                {% if part.cost_price %}
                                <div class="text-xs text-gray-500">Cost: KSh {{ part.cost_price|floatformat:0 }}</div>
                                {% endif %}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                {% if part.supplier %}
                                <div class="text-sm text-gray-900 font-raleway">{{ part.supplier.name }}</div>
                                <div class="text-xs text-gray-500">{{ part.supplier.contact_email }}</div>
                                {% else %}
                                <span class="text-sm text-gray-500">No supplier</span>
                                {% endif %}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex flex-wrap gap-2 justify-center">
                                    <button onclick="openEditPartModal({{ part.id }})"
                                            class="pill-action-btn pill-btn-edit">
                                        <i class="fas fa-edit mr-1"></i>Edit
                                    </button>
                                    <button onclick="openRestockModal({{ part.id }})"
                                            class="pill-action-btn pill-btn-restock">
                                        <i class="fas fa-plus-circle mr-1"></i>Restock
                                    </button>
                                    <button onclick="openViewPartModal({{ part.id }})"
                                            class="pill-action-btn pill-btn-view">
                                        <i class="fas fa-eye mr-1"></i>View
                                    </button>
                                    <button onclick="deleteSparePartConfirm({{ part.id }})"
                                            class="pill-action-btn pill-btn-delete">
                                        <i class="fas fa-trash mr-1"></i>Delete
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    {% else %}
                        <tr>
                            <td colspan="6" class="px-6 py-12 text-center">
                                <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <i class="fas fa-cogs text-gray-400 text-2xl"></i>
                                </div>
                                <h4 class="text-lg font-semibold text-gray-900 mb-2 font-montserrat">No spare parts yet</h4>
                                <p class="text-gray-600 font-raleway">Add spare parts to your inventory to get started.</p>
                                <button class="mt-4 btn-admin-primary">
                                    <i class="fas fa-plus mr-2"></i>Add First Spare Part
                                </button>
                            </td>
                        </tr>
                    {% endif %}
                </tbody>
            </table>
        </div>
    </div>

    <!-- Recent Orders -->
    <div class="admin-card animate-fade-in-up" style="animation-delay: 0.5s;">
        <div class="p-6 border-b border-gray-200">
            <div class="flex items-center">
                <div class="w-10 h-10 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center mr-3">
                    <i class="fas fa-shopping-cart text-white"></i>
                </div>
                <h3 class="text-lg font-bold text-harrier-dark font-montserrat">Recent Spare Parts Orders</h3>
            </div>
        </div>
        
        <div class="p-6">
            {% if recent_orders %}
                <div class="space-y-4">
                    {% for order in recent_orders %}
                    <div class="order-card border border-gray-200 rounded-lg p-4 hover:border-green-300 hover:shadow-md transition-all duration-300">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-4">
                                <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center text-white font-semibold">
                                    {{ order.customer.first_name|first|default:order.customer.username|first|upper }}
                                </div>
                                <div>
                                    <h4 class="font-semibold text-harrier-dark font-raleway">Order #{{ order.id|stringformat:"05d" }}</h4>
                                    <p class="text-sm text-gray-600">{{ order.customer.get_full_name|default:order.customer.username }}</p>
                                    <p class="text-xs text-gray-500">{{ order.created_at|timesince }} ago</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="text-lg font-semibold text-harrier-red">KSh {{ order.total_amount|floatformat:0 }}</div>
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                    {% if order.status == 'pending' %}bg-yellow-100 text-yellow-800
                                    {% elif order.status == 'processing' %}bg-blue-100 text-blue-800
                                    {% elif order.status == 'completed' %}bg-green-100 text-green-800
                                    {% else %}bg-gray-100 text-gray-800{% endif %}">
                                    {{ order.get_status_display }}
                                </span>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="text-center py-8">
                    <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-shopping-cart text-gray-400 text-2xl"></i>
                    </div>
                    <h4 class="text-lg font-semibold text-gray-900 mb-2 font-montserrat">No recent orders</h4>
                    <p class="text-gray-600 font-raleway">Spare parts orders will appear here when customers make purchases.</p>
                </div>
            {% endif %}
        </div>
    </div>

    <!-- Recent Spare Parts Orders Section -->
    <div class="glassmorphism-card animate-fade-in-up" style="animation-delay: 0.5s;">
        <div class="p-6 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-gradient-to-br from-teal-500 to-teal-600 rounded-xl flex items-center justify-center mr-4 shadow-lg">
                        <i class="fas fa-shopping-cart text-white text-xl"></i>
                    </div>
                    <div>
                        <h3 class="text-xl font-bold text-harrier-dark font-montserrat">Recent Spare Parts Orders</h3>
                        <p class="text-sm text-gray-600 font-raleway">Latest customer orders and their status</p>
                    </div>
                </div>

                <div class="flex gap-2">
                    <button onclick="refreshOrdersData()" class="enhanced-btn enhanced-btn-secondary text-sm px-3 py-2">
                        <i class="fas fa-sync-alt mr-1"></i>Refresh
                    </button>
                    <button onclick="viewAllOrders()" class="enhanced-btn enhanced-btn-secondary text-sm px-3 py-2">
                        <i class="fas fa-external-link-alt mr-1"></i>View All
                    </button>
                </div>
            </div>
        </div>

        <div class="p-6">
            {% if recent_orders %}
                <div class="space-y-4">
                    {% for order in recent_orders %}
                    <div class="order-card glassmorphism-card p-4 hover:shadow-lg transition-all duration-300">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-4">
                                <!-- Order Status Icon -->
                                <div class="flex-shrink-0">
                                    <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                                        <i class="fas fa-check-circle text-green-600"></i>
                                    </div>
                                </div>

                                <!-- Order Details -->
                                <div class="flex-1 min-w-0">
                                    <div class="flex items-center space-x-2">
                                        <h4 class="text-sm font-semibold text-harrier-dark font-montserrat">
                                            Order #SP-{{ order.id|default:"001" }}
                                        </h4>
                                        <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            Completed
                                        </span>
                                    </div>
                                    <p class="text-sm text-gray-600 font-raleway mt-1">
                                        Customer: John Doe
                                    </p>
                                    <p class="text-xs text-gray-500 mt-1">
                                        Dec 15, 2024 2:30 PM
                                    </p>
                                </div>
                            </div>

                            <!-- Order Summary -->
                            <div class="flex items-center space-x-6">
                                <!-- Items Count -->
                                <div class="text-center">
                                    <div class="text-lg font-semibold text-harrier-dark font-montserrat">3</div>
                                    <div class="text-xs text-gray-600">Items</div>
                                </div>

                                <!-- Total Amount -->
                                <div class="text-center">
                                    <div class="text-lg font-semibold text-green-600 font-montserrat">KSh 4,500</div>
                                    <div class="text-xs text-gray-600">Total</div>
                                </div>

                                <!-- Quick Actions -->
                                <div class="flex space-x-2">
                                    <button onclick="viewOrderDetails('SP-001')"
                                            class="pill-action-btn pill-btn-view text-xs px-2 py-1">
                                        <i class="fas fa-eye mr-1"></i>View
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            {% else %}
                <!-- Empty State -->
                <div class="text-center py-12">
                    <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-shopping-cart text-gray-400 text-2xl"></i>
                    </div>
                    <h4 class="text-lg font-semibold text-gray-900 mb-2 font-montserrat">No Recent Orders</h4>
                    <p class="text-gray-600 font-raleway mb-4">
                        No spare parts orders have been placed recently.
                    </p>
                    <button onclick="viewAllOrders()" class="enhanced-btn enhanced-btn-primary">
                        <i class="fas fa-external-link-alt mr-2"></i>View All Orders
                    </button>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
{{ block.super }}
<style>
    /* Enhanced Glassmorphism Cards */
    .glassmorphism-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 16px;
        box-shadow:
            0 8px 32px rgba(0, 0, 0, 0.1),
            0 4px 16px rgba(0, 0, 0, 0.05),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .glassmorphism-card:hover {
        transform: translateY(-4px);
        box-shadow:
            0 20px 40px rgba(0, 0, 0, 0.15),
            0 8px 24px rgba(0, 0, 0, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.4);
        border-color: rgba(220, 38, 38, 0.2);
    }

    /* Enhanced Statistics Cards */
    .stat-card-enhanced {
        padding: 1.5rem;
        position: relative;
        overflow: hidden;
    }

    .stat-card-enhanced::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg,
            var(--harrier-red) 0%,
            var(--harrier-blue) 50%,
            var(--harrier-dark) 100%);
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .stat-card-enhanced:hover::before {
        opacity: 1;
    }

    .stat-value {
        font-size: 2rem;
        font-weight: 800;
        line-height: 1;
        margin-bottom: 0.5rem;
    }

    .stat-label {
        font-size: 0.875rem;
        font-weight: 600;
        color: #6B7280;
        text-transform: uppercase;
        letter-spacing: 0.05em;
    }

    .stat-trend {
        font-weight: 500;
    }

    .stat-icon {
        width: 3rem;
        height: 3rem;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .stat-icon::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
        transition: left 0.6s ease;
    }

    .stat-card-enhanced:hover .stat-icon::before {
        left: 100%;
    }

    /* Progress Bar Styles */
    .stat-progress-bar {
        width: 100%;
        height: 4px;
        background: rgba(0, 0, 0, 0.1);
        border-radius: 2px;
        margin-top: 1rem;
        overflow: hidden;
        position: relative;
    }

    .stat-progress {
        height: 100%;
        border-radius: 2px;
        transition: width 1s ease-in-out;
        position: relative;
        overflow: hidden;
    }

    .stat-progress::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(90deg,
            transparent 0%,
            rgba(255, 255, 255, 0.3) 50%,
            transparent 100%);
        animation: shimmer 2s infinite;
    }

    @keyframes shimmer {
        0% { transform: translateX(-100%); }
        100% { transform: translateX(100%); }
    }

    /* Enhanced Stat Card Animations */
    .stat-card-enhanced {
        position: relative;
        overflow: hidden;
    }

    .stat-card-enhanced::after {
        content: '';
        position: absolute;
        top: 0;
        right: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg,
            transparent,
            rgba(255, 255, 255, 0.1),
            transparent);
        transition: right 0.8s ease;
    }

    .stat-card-enhanced:hover::after {
        right: 100%;
    }

    /* Real-time Update Indicators */
    .stat-updating {
        position: relative;
    }

    .stat-updating::before {
        content: '';
        position: absolute;
        top: -2px;
        left: -2px;
        right: -2px;
        bottom: -2px;
        background: linear-gradient(45deg,
            var(--harrier-red),
            var(--harrier-blue),
            var(--harrier-red));
        background-size: 400% 400%;
        border-radius: 18px;
        z-index: -1;
        animation: gradient-pulse 2s ease infinite;
    }

    @keyframes gradient-pulse {
        0%, 100% { background-position: 0% 50%; }
        50% { background-position: 100% 50%; }
    }

    /* Loading Skeleton for Stats */
    .stat-skeleton {
        background: linear-gradient(90deg,
            #f0f0f0 25%,
            #e0e0e0 50%,
            #f0f0f0 75%);
        background-size: 200% 100%;
        animation: loading 1.5s infinite;
        border-radius: 4px;
        height: 1.5rem;
        width: 60%;
    }

    .stat-skeleton.wide {
        width: 80%;
    }

    .stat-skeleton.narrow {
        width: 40%;
        height: 1rem;
    }

    /* Enhanced Quick Action Cards */
    .quick-action-card-enhanced {
        padding: 1.5rem;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
    }

    .quick-action-card-enhanced::after {
        content: '';
        position: absolute;
        top: 0;
        right: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg,
            transparent,
            rgba(255, 255, 255, 0.4),
            transparent);
        transition: right 0.6s ease;
    }

    .quick-action-card-enhanced:hover::after {
        right: 100%;
    }

    .action-icon {
        width: 3.5rem;
        height: 3.5rem;
        border-radius: 14px;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
        transition: all 0.3s ease;
    }

    .action-arrow {
        transition: all 0.3s ease;
    }

    /* Enhanced Button Styles */
    .enhanced-btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 0.75rem 1.25rem;
        font-weight: 600;
        font-size: 0.875rem;
        line-height: 1.2;
        border-radius: 10px;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        font-family: 'Montserrat', sans-serif;
        border: 2px solid transparent;
        cursor: pointer;
        position: relative;
        overflow: hidden;
    }

    .enhanced-btn-primary {
        background: linear-gradient(135deg,
            var(--harrier-red) 0%,
            var(--harrier-dark) 100%);
        color: white;
        box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);
    }

    .enhanced-btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(220, 38, 38, 0.4);
    }

    .enhanced-btn-secondary {
        background: rgba(255, 255, 255, 0.9);
        color: var(--harrier-dark);
        border-color: rgba(156, 163, 175, 0.3);
        backdrop-filter: blur(10px);
    }

    .enhanced-btn-secondary:hover {
        background: rgba(255, 255, 255, 1);
        border-color: var(--harrier-red);
        color: var(--harrier-red);
        transform: translateY(-1px);
    }

    /* Enhanced Select Styling */
    .enhanced-select {
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(10px);
        transition: all 0.3s ease;
    }

    .enhanced-select:focus {
        background: rgba(255, 255, 255, 1);
        box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
    }

    /* Pill Action Buttons */
    .pill-action-btn {
        display: inline-flex;
        align-items: center;
        padding: 0.375rem 0.75rem;
        font-size: 0.75rem;
        font-weight: 600;
        border-radius: 9999px;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        font-family: 'Montserrat', sans-serif;
        text-transform: uppercase;
        letter-spacing: 0.025em;
        border: 1px solid transparent;
        cursor: pointer;
        position: relative;
        overflow: hidden;
    }

    .pill-btn-edit {
        background: linear-gradient(135deg, #3B82F6, #1E40AF);
        color: white;
        box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
    }

    .pill-btn-edit:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
    }

    .pill-btn-restock {
        background: linear-gradient(135deg, #10B981, #047857);
        color: white;
        box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
    }

    .pill-btn-restock:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
    }

    .pill-btn-view {
        background: linear-gradient(135deg, #8B5CF6, #6D28D9);
        color: white;
        box-shadow: 0 2px 8px rgba(139, 92, 246, 0.3);
    }

    .pill-btn-view:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(139, 92, 246, 0.4);
    }

    .pill-btn-delete {
        background: linear-gradient(135deg, #EF4444, #DC2626);
        color: white;
        box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
    }

    .pill-btn-delete:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(239, 68, 68, 0.4);
    }

    /* Table Enhancements */
    tbody tr {
        transition: all 0.3s ease;
    }

    tbody tr:hover {
        background: linear-gradient(90deg,
            rgba(220, 38, 38, 0.02) 0%,
            rgba(255, 255, 255, 1) 50%,
            rgba(220, 38, 38, 0.02) 100%);
        transform: translateX(4px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }

    /* Loading States */
    .loading-skeleton {
        background: linear-gradient(90deg,
            #f0f0f0 25%,
            #e0e0e0 50%,
            #f0f0f0 75%);
        background-size: 200% 100%;
        animation: loading 1.5s infinite;
    }

    @keyframes loading {
        0% { background-position: 200% 0; }
        100% { background-position: -200% 0; }
    }

    /* Responsive Design */
    @media (max-width: 1024px) {
        .pill-action-btn {
            padding: 0.25rem 0.5rem;
            font-size: 0.7rem;
        }

        .pill-action-btn i {
            margin-right: 0.25rem;
        }
    }

    @media (max-width: 768px) {
        .stat-card-enhanced {
            padding: 1rem;
        }

        .quick-action-card-enhanced {
            padding: 1rem;
        }

        .action-icon {
            width: 2.5rem;
            height: 2.5rem;
        }

        .pill-action-btn span {
            display: none;
        }

        .pill-action-btn {
            padding: 0.375rem;
            border-radius: 50%;
            width: 2rem;
            height: 2rem;
        }
    }

    /* Animation Delays */
    .animate-fade-in-up {
        animation: fadeInUp 0.6s ease-out forwards;
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Enhanced Filter System Styles */
    .filter-group {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }

    .filter-label {
        display: flex;
        align-items: center;
        font-size: 0.75rem;
        font-weight: 600;
        color: #374151;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        font-family: 'Montserrat', sans-serif;
    }

    .enhanced-select {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border: 1px solid #D1D5DB;
        border-radius: 8px;
        padding: 0.5rem 0.75rem;
        font-size: 0.875rem;
        font-family: 'Raleway', sans-serif;
        transition: all 0.3s ease;
        appearance: none;
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
        background-position: right 0.5rem center;
        background-repeat: no-repeat;
        background-size: 1.5em 1.5em;
        padding-right: 2.5rem;
    }

    .enhanced-select:focus {
        background: rgba(255, 255, 255, 1);
        border-color: var(--harrier-red);
        box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
        outline: none;
    }

    .enhanced-select:hover {
        border-color: #9CA3AF;
    }

    /* Advanced Filters Panel Animation */
    #advanced-filters-panel {
        max-height: 0;
        overflow: hidden;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        opacity: 0;
    }

    #advanced-filters-panel.show {
        max-height: 500px;
        opacity: 1;
    }

    /* Filter Toggle Animation */
    #filter-chevron.rotated {
        transform: rotate(180deg);
    }

    /* Search Input Enhancements */
    #inventory-search {
        transition: all 0.3s ease;
    }

    #inventory-search:focus {
        box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
        transform: translateY(-1px);
    }

    /* HTMX Loading Indicator */
    .htmx-indicator {
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .htmx-request .htmx-indicator {
        opacity: 1;
    }

    .htmx-request #inventory-table-container {
        opacity: 0.7;
        pointer-events: none;
        transition: opacity 0.3s ease;
    }

    /* Filter Count Badge */
    #filter-count {
        font-weight: 600;
        color: var(--harrier-red);
    }

    /* Mobile Filter Responsiveness */
    @media (max-width: 768px) {
        .filter-group {
            margin-bottom: 1rem;
        }

        #advanced-filters-panel .grid {
            grid-template-columns: 1fr;
            gap: 1rem;
        }

        .enhanced-select {
            padding: 0.75rem;
            font-size: 1rem;
        }

        #inventory-search {
            padding: 0.75rem 3rem 0.75rem 3rem;
            font-size: 1rem;
        }
    }

    /* Filter Preset Styles */
    .filter-preset {
        display: inline-flex;
        align-items: center;
        padding: 0.25rem 0.75rem;
        background: rgba(220, 38, 38, 0.1);
        color: var(--harrier-red);
        border-radius: 9999px;
        font-size: 0.75rem;
        font-weight: 500;
        margin: 0.25rem;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .filter-preset:hover {
        background: rgba(220, 38, 38, 0.2);
        transform: translateY(-1px);
    }

    .filter-preset .remove-preset {
        margin-left: 0.5rem;
        opacity: 0.7;
        transition: opacity 0.3s ease;
    }

    .filter-preset:hover .remove-preset {
        opacity: 1;
    }

    /* Accessibility */
    @media (prefers-reduced-motion: reduce) {
        * {
            animation-duration: 0.01ms !important;
            animation-iteration-count: 1 !important;
            transition-duration: 0.01ms !important;
        }
    }
</style>
{% endblock %}

{% block extra_js %}
{{ block.super }}
<script>
// Enhanced Spare Shop Management JavaScript
document.addEventListener('DOMContentLoaded', function() {
    initializeSpareShopManagement();
});

function initializeSpareShopManagement() {
    // Initialize real-time search
    initializeRealTimeSearch();

    // Initialize advanced filters
    initializeAdvancedFilters();

    // Initialize tooltips
    initializeTooltips();

    // Initialize loading states
    initializeLoadingStates();

    // Initialize filter state management
    initializeFilterStateManagement();
}

function initializeRealTimeSearch() {
    const searchInput = document.getElementById('inventory-search');
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            showLoadingState();
        });
    }
}

function initializeTooltips() {
    // Initialize any tooltips if needed
    const tooltipElements = document.querySelectorAll('[title]');
    tooltipElements.forEach(element => {
        element.addEventListener('mouseenter', function() {
            // Add tooltip functionality if needed
        });
    });
}

function initializeLoadingStates() {
    // Add loading state management
    document.addEventListener('htmx:beforeRequest', function() {
        showLoadingState();
    });

    document.addEventListener('htmx:afterRequest', function() {
        hideLoadingState();
    });
}

function showLoadingState() {
    const container = document.getElementById('inventory-table-container');
    if (container) {
        container.style.opacity = '0.7';
        container.style.pointerEvents = 'none';
    }
}

function hideLoadingState() {
    const container = document.getElementById('inventory-table-container');
    if (container) {
        container.style.opacity = '1';
        container.style.pointerEvents = 'auto';
    }
}

// Enhanced Modal Functions
function openAddPartModal() {
    fetch('/dashboard/admin/spare-parts/add-modal/')
        .then(response => response.text())
        .then(html => {
            document.body.insertAdjacentHTML('beforeend', html);

            // Process the new content with HTMX
            const newModal = document.body.lastElementChild;
            if (newModal && typeof htmx !== 'undefined') {
                htmx.process(newModal);
                console.log('HTMX processed new admin modal content');
            }
        })
        .catch(error => {
            console.error('Error loading modal:', error);
            showToast('Error loading add part modal', 'error');
        });
}

function openEditPartModal(partId) {
    fetch(`/dashboard/admin/spare-parts/edit/${partId}/modal/`)
        .then(response => response.text())
        .then(html => {
            document.body.insertAdjacentHTML('beforeend', html);
        })
        .catch(error => {
            console.error('Error loading modal:', error);
            showToast('Error loading edit part modal', 'error');
        });
}

function openRestockModal(partId) {
    fetch(`/dashboard/admin/spare-parts/restock/${partId}/modal/`)
        .then(response => response.text())
        .then(html => {
            document.body.insertAdjacentHTML('beforeend', html);
        })
        .catch(error => {
            console.error('Error loading modal:', error);
            showToast('Error loading restock modal', 'error');
        });
}

function openViewPartModal(partId) {
    fetch(`/dashboard/admin/spare-parts/view/${partId}/modal/`)
        .then(response => response.text())
        .then(html => {
            document.body.insertAdjacentHTML('beforeend', html);
        })
        .catch(error => {
            console.error('Error loading modal:', error);
            showToast('Error loading view part modal', 'error');
        });
}

function deleteSparePartConfirm(partId) {
    if (confirm('Are you sure you want to delete this spare part?')) {
        showToast(`Delete functionality for ID: ${partId} - Implementation in Phase 3`, 'warning');
    }
}

// Advanced Filter System Functions
function initializeAdvancedFilters() {
    // Check if advanced filters should be shown based on URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const hasAdvancedFilters = urlParams.get('category') || urlParams.get('brand') ||
                              urlParams.get('price_range') || urlParams.get('condition');

    if (hasAdvancedFilters) {
        showAdvancedFilters();
    }

    // Update filter count
    updateFilterCount();
}

function toggleAdvancedFilters() {
    const panel = document.getElementById('advanced-filters-panel');
    const chevron = document.getElementById('filter-chevron');

    if (panel.classList.contains('show')) {
        hideAdvancedFilters();
    } else {
        showAdvancedFilters();
    }
}

function showAdvancedFilters() {
    const panel = document.getElementById('advanced-filters-panel');
    const chevron = document.getElementById('filter-chevron');

    panel.classList.remove('hidden');
    setTimeout(() => {
        panel.classList.add('show');
        chevron.classList.add('rotated');
    }, 10);
}

function hideAdvancedFilters() {
    const panel = document.getElementById('advanced-filters-panel');
    const chevron = document.getElementById('filter-chevron');

    panel.classList.remove('show');
    chevron.classList.remove('rotated');

    setTimeout(() => {
        panel.classList.add('hidden');
    }, 400);
}

function clearSearch() {
    const searchInput = document.getElementById('inventory-search');
    searchInput.value = '';
    searchInput.dispatchEvent(new Event('input'));
}

function clearAllFilters() {
    // Clear all filter inputs
    document.querySelectorAll('select[name]').forEach(select => {
        select.value = '';
    });

    // Clear search
    clearSearch();

    // Trigger update
    const searchInput = document.getElementById('inventory-search');
    searchInput.dispatchEvent(new Event('input'));

    // Hide advanced filters
    hideAdvancedFilters();

    showToast('All filters cleared', 'success');
}

function initializeFilterStateManagement() {
    // Save filter state to localStorage
    document.addEventListener('htmx:afterRequest', function(event) {
        if (event.target.id === 'inventory-table-container') {
            saveFilterState();
            updateFilterCount();
        }
    });
}

function saveFilterState() {
    const filterState = {
        search: document.getElementById('inventory-search').value,
        category: document.querySelector('select[name="category"]').value,
        availability: document.querySelector('select[name="availability"]').value,
        brand: document.querySelector('select[name="brand"]').value,
        price_range: document.querySelector('select[name="price_range"]').value,
        condition: document.querySelector('select[name="condition"]').value
    };

    localStorage.setItem('spare_parts_filters', JSON.stringify(filterState));
}

function loadFilterState() {
    const savedState = localStorage.getItem('spare_parts_filters');
    if (savedState) {
        const filterState = JSON.parse(savedState);

        // Apply saved filters
        Object.keys(filterState).forEach(key => {
            const element = key === 'search' ?
                document.getElementById('inventory-search') :
                document.querySelector(`select[name="${key}"]`);

            if (element && filterState[key]) {
                element.value = filterState[key];
            }
        });
    }
}

function updateFilterCount() {
    // Update the filter count display
    const countElement = document.getElementById('filter-count');
    if (countElement) {
        // This will be updated by HTMX response
        // But we can add visual feedback here
        countElement.style.color = 'var(--harrier-red)';
        countElement.style.fontWeight = '600';
    }
}

function saveFilterPreset() {
    const presetName = prompt('Enter a name for this filter preset:');
    if (presetName) {
        const filterState = {
            search: document.getElementById('inventory-search').value,
            category: document.querySelector('select[name="category"]').value,
            availability: document.querySelector('select[name="availability"]').value,
            brand: document.querySelector('select[name="brand"]').value,
            price_range: document.querySelector('select[name="price_range"]').value,
            condition: document.querySelector('select[name="condition"]').value
        };

        // Save to localStorage
        const presets = JSON.parse(localStorage.getItem('filter_presets') || '{}');
        presets[presetName] = filterState;
        localStorage.setItem('filter_presets', JSON.stringify(presets));

        showToast(`Filter preset "${presetName}" saved successfully`, 'success');
    }
}

function bulkActions() {
    showToast('Bulk Actions - Implementation in Phase 6', 'info');
}

function openMpesaConfigModal() {
    fetch('/dashboard/admin/spare-shop/mpesa-config-modal/')
        .then(response => response.text())
        .then(html => {
            document.body.insertAdjacentHTML('beforeend', html);
        })
        .catch(error => {
            console.error('Error loading modal:', error);
            showToast('Error loading M-Pesa configuration modal', 'error');
        });
}

function openSalesManagementPage() {
    window.location.href = '/dashboard/admin/sales-management/';
}

function openOrderManagementPage() {
    window.location.href = '/dashboard/admin/order-management/';
}

function testMpesaConnection() {
    showToast('Testing M-Pesa connection...', 'info');
    // Simulate connection test
    setTimeout(() => {
        showToast('M-Pesa connection test successful!', 'success');
    }, 2000);
}

// Recent Orders Functions
function refreshOrdersData() {
    showToast('Refreshing orders data...', 'info');
    // In real implementation, this would refresh the orders section
    setTimeout(() => {
        showToast('Orders data refreshed successfully', 'success');
    }, 1000);
}

function viewAllOrders() {
    window.location.href = '/dashboard/admin/order-management/';
}

function viewOrderDetails(orderId) {
    showToast(`Viewing details for order ${orderId}`, 'info');
    // In real implementation, this would open order details modal
}

function updateOrderStatus(orderId) {
    showToast(`Update status for order ${orderId}`, 'info');
    // In real implementation, this would open status update modal
}

// Enhanced Utility Functions
function refreshInventory() {
    showLoadingState();

    // Use HTMX to refresh the table
    htmx.ajax('GET', '/dashboard/admin/spare-shop/', {
        target: '#inventory-table-container',
        swap: 'outerHTML'
    }).then(() => {
        hideLoadingState();
        showToast('Inventory refreshed successfully', 'success');
        updateFilterCount();
    }).catch(() => {
        hideLoadingState();
        showToast('Error refreshing inventory', 'error');
    });
}

function exportInventoryData() {
    const exportModal = createExportModal();
    document.body.appendChild(exportModal);
}

function createExportModal() {
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 z-50 overflow-y-auto';
    modal.innerHTML = `
        <div class="fixed inset-0 bg-black bg-opacity-50 transition-opacity" onclick="this.parentElement.remove()"></div>
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-2xl max-w-md w-full shadow-2xl">
                <div class="bg-gradient-to-r from-blue-600 to-blue-800 px-6 py-4 rounded-t-2xl">
                    <div class="flex items-center justify-between">
                        <h3 class="text-xl font-bold text-white font-montserrat">Export Inventory Data</h3>
                        <button onclick="this.closest('.fixed').remove()" class="text-white hover:bg-white hover:bg-opacity-20 rounded-lg p-2">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Export Format</label>
                            <select id="export-format" class="w-full border border-gray-300 rounded-lg px-3 py-2">
                                <option value="csv">CSV (Comma Separated Values)</option>
                                <option value="excel">Excel Spreadsheet</option>
                                <option value="pdf">PDF Report</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Include Fields</label>
                            <div class="space-y-2">
                                <label class="flex items-center">
                                    <input type="checkbox" checked class="mr-2"> Basic Information
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" checked class="mr-2"> Stock Levels
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" checked class="mr-2"> Pricing
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" class="mr-2"> Supplier Information
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="flex gap-3 mt-6">
                        <button onclick="this.closest('.fixed').remove()" class="flex-1 px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50">
                            Cancel
                        </button>
                        <button onclick="performExport()" class="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                            <i class="fas fa-download mr-2"></i>Export
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;
    return modal;
}

function performExport() {
    const format = document.getElementById('export-format').value;
    const checkboxes = document.querySelectorAll('input[type="checkbox"]:checked');
    const fields = Array.from(checkboxes).map(cb => cb.parentElement.textContent.trim());

    showToast(`Exporting inventory data as ${format.toUpperCase()}...`, 'info');

    // Simulate export process
    setTimeout(() => {
        showToast(`Inventory data exported successfully as ${format.toUpperCase()}`, 'success');
        document.querySelector('.fixed.inset-0.z-50').remove();

        // In real implementation, this would trigger a download
        const link = document.createElement('a');
        link.href = `/dashboard/admin/spare-shop/export/?format=${format}&fields=${fields.join(',')}`;
        link.download = `inventory_${new Date().toISOString().split('T')[0]}.${format}`;
        link.click();
    }, 2000);
}

// Toast notification function (if not already available)
function showToast(message, type = 'info') {
    // Create toast element
    const toast = document.createElement('div');
    toast.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg transition-all duration-300 transform translate-x-full`;

    // Set toast styling based on type
    switch(type) {
        case 'success':
            toast.className += ' bg-green-500 text-white';
            break;
        case 'error':
            toast.className += ' bg-red-500 text-white';
            break;
        case 'warning':
            toast.className += ' bg-orange-500 text-white';
            break;
        default:
            toast.className += ' bg-blue-500 text-white';
    }

    toast.innerHTML = `
        <div class="flex items-center">
            <i class="fas fa-info-circle mr-2"></i>
            <span>${message}</span>
        </div>
    `;

    document.body.appendChild(toast);

    // Animate in
    setTimeout(() => {
        toast.classList.remove('translate-x-full');
    }, 100);

    // Remove after 3 seconds
    setTimeout(() => {
        toast.classList.add('translate-x-full');
        setTimeout(() => {
            document.body.removeChild(toast);
        }, 300);
    }, 3000);
}
</script>
{% endblock %}
